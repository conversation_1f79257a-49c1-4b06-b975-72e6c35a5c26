import { useRef, useState } from "react";

export default function UseRef() {
  const timer = useRef<null | NodeJS.Timeout>(null);
  const [count, setCount] = useState(0);

  const start = () => {
    timer.current = setInterval(() => {
      setCount((count) => count + 1);
    }, 100);
  }

  const end = () => {
    clearInterval(timer.current!);
  }

  return (
    <div>
      <div>{count}</div>
      <button onClick={start}>start</button>
      <button onClick={end}>end</button>
    </div>
  )

	// const ref = useRef<HTMLInputElement>(null)
	// const handleClick = () => {
	//   ref.current!.style.background = 'red'
	// }
	// return (
	//   <div>
	//     <div ref={ref} onClick={handleClick}>ref</div>
	//   </div>
	// )
}
