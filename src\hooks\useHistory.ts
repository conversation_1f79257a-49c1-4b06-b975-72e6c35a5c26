import { useSyncExternalStore } from "react"

export const useHistory = () => {
  const subscribe = (callback: () => void) => {
    // 监听 url 变化
    window.addEventListener('popstate', callback)
    window.addEventListener('hashchange', callback)
    // 取消监听
    return () => {
      window.removeEventListener('popstate', callback)
      window.removeEventListener('hashchange', callback)
    }
  }

  const getSnapshot = () => {
    return window.location.href

    // 如果是引用类型，要添加是否相等判断，否则会一直触发
    // if (obj.todos !== lastTodos) {
    //   lastSnapshot = { todos: obj.todos.slice()}
    //   lastTodos = obj.todos
    // }
    // return lastSnapshot
  }

  const url = useSyncExternalStore(subscribe, getSnapshot)

  const push = (url: string) => {
    window.history.pushState({}, '', url)
    window.dispatchEvent(new PopStateEvent('popstate'))
  }
  const replace = (url: string) => {
    window.history.replaceState({}, '', url)
    window.dispatchEvent(new PopStateEvent('popstate'))

  }

  return [url, push, replace] as const
}