import { Input, List } from "antd";
import { useState, useTransition } from "react";

interface ListItem {
	id: number;
	name: string;
	address: string;
}

export default function UseTransition() {
	const [val, setVal] = useState("");
	const [list, setList] = useState<ListItem[]>([]);
	const [isPending, startTransition] = useTransition();

	const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setVal(e.target.value);
		fetch(`/api/list?keyword=${e.target.value}`)
			.then((res) => res.json())
			.then((res) => {
				startTransition(() => {
					setList(res.list);
				});
			});
	};

	return (
		<div>
			<Input value={val} onChange={handleChange} />
      {isPending && <div>Loading...</div>}
			<List
				bordered
				dataSource={list}
				renderItem={(item) => (
					<List.Item key={item.id}>
						<List.Item.Meta title={item.name} description={item.address} />
					</List.Item>
				)}
			/>
		</div>
	);
}
