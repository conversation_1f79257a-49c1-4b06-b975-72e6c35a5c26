{"name": "react-template", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "biome lint --write .", "format": "biome format --write .", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.3", "antd": "^5.24.8", "mockjs": "^1.1.0", "react": "^19.0.0", "react-dom": "^19.0.0", "tailwindcss": "^4.1.3"}, "devDependencies": {"@biomejs/biome": "1.9.4", "@types/mockjs": "^1.0.10", "@types/node": "^22.14.1", "@types/react": "^19.0.10", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react": "^4.3.4", "globals": "^15.15.0", "typescript": "~5.7.2", "typescript-eslint": "^8.24.1", "vite": "^6.2.0"}}