import { Button } from "antd";
import "./App.css";
import UseCallback from "./components/UseCallback";
import UseContext from "./components/UseContext";
import UseImperativeHandle from "./components/UseImperativeHandle";
import UseLayoutEffect from "./components/UseLayoutEffect";
import UseMemo from "./components/UseMemo";
import UseRef from "./components/UseRef";
import { createContext, useState } from "react";

interface ThemeContext {
	theme: string;
	setTheme: (theme: string) => void;
}

export const ThemeContext = createContext({} as ThemeContext);

function App() {
	const [theme, setTheme] = useState("light");

	return (
		<>
			<ThemeContext.Provider value={{ theme, setTheme }}>
				<UseCallback />
				<Button type="primary" onClick={() => window.onShow()}>use Message</Button>
			</ThemeContext.Provider>
		</>
	);
}

export default App;
