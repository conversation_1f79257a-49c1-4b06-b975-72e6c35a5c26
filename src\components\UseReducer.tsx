import { useMemo, useReducer } from "react";

const initState = [
	{ name: "张三", price: 100, count: 1, id: 1, isEdit: false },
	{ name: "李四", price: 200, count: 2, id: 2, isEdit: false },
	{ name: "王五", price: 300, count: 3, id: 3, isEdit: false },
];

type InitStateType = typeof initState;

const reducer = (
	state: InitStateType,
	action: {
		type: "add" | "minus" | "delete" | "edit" | "update_name" | "blur";
		id: number;
		newName?: string;
	},
) => {
	switch (action.type) {
		case "add":
			return state.map((item) =>
				item.id === action.id ? { ...item, count: item.count + 1 } : item,
			);
		case "minus":
			return state.map((item) =>
				item.id === action.id ? { ...item, count: item.count - 1 } : item,
			);
		case "delete":
			return state.filter((item) => item.id !== action.id);
		case "edit":
			return state.map((item) =>
				item.id === action.id ? { ...item, isEdit: !item.isEdit } : item,
			);
		case "update_name":
			return state.map((item) =>
				item.id === action.id ? { ...item, name: action.newName! } : item,
			);
		case "blur":
			return state.map((item) =>
				item.id === action.id ? { ...item, isEdit: false } : item,
			);
		default:
			return state;
	}
};


export default function UseReducerComponent() {
	const [state, dispatch] = useReducer(reducer, initState);
	const total = useMemo(() => {
		return state.reduce((prev, curr) => prev + curr.price * curr.count, 0)
	}, [state])
	
	return (
		<table cellSpacing={0} width={800} border={1}>
			<thead>
				<tr>
					<th>商品</th>
					<th>单价</th>
					<th>数量</th>
					<th>小计</th>
					<th>操作</th>
				</tr>
			</thead>
			<tbody>
				{state.map((item) => (
					<tr key={item.id}>
						<td
							align="center"
							onDoubleClick={() => dispatch({ type: "edit", id: item.id })}
							onBlur={() => dispatch({ type: "blur", id: item.id })}
						>
							{item.isEdit ? (
								<input
									type="text"
									value={item.name}
									onChange={(e) =>
										dispatch({
											type: "update_name",
											id: item.id,
											newName: e.target.value,
										})
									}
								/>
							) : (
								item.name
							)}
						</td>
						<td align="center">{item.price}</td>
						<td align="center">
							<button
								onClick={() => dispatch({ type: "add", id: item.id })}
								style={{ marginRight: "10px" }}
							>
								+1
							</button>
							{item.count}
							<button
								onClick={() => dispatch({ type: "minus", id: item.id })}
								style={{ marginLeft: "10px" }}
							>
								-1
							</button>
						</td>
						<td align="center">{item.price * item.count}</td>
						<td align="center">
							<button onClick={() => dispatch({type: 'edit', id: item.id })}>编辑</button>
							<button onClick={() => dispatch({type: 'delete', id: item.id })}>删除</button>
						</td>
					</tr>
				))}
			</tbody>
			<tfoot>
				<tr>
					<td colSpan={4} />
					<td align="right">
						总价:{" "}
						{total}
					</td>
				</tr>
			</tfoot>
		</table>
	);
}
