import { useState } from "react";

export default function useStateComponent() {
	const [str, setStr] = useState(() => {
		const date = new Date()
		return {
			date: date.toLocaleString(),
			name: "<PERSON>",
		}
	})

	const [num, setNum] = useState(0)

	const handleClick = () => {
		setStr({
			...str,
			name: "<PERSON>",
		})
		setNum((prev) => prev + 1)
		setNum((prev) => prev + 1)
		console.log(str)
	}

	return (
		<>
			<div>{str.date}</div>
			<div>{str.name}</div>
			<button onClick={handleClick}>Click me</button>
		</>
	);
}
