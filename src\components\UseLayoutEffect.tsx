import { useLayoutEffect, useRef } from "react";
export default function UseLayoutEffect() {
  const container = useRef<HTMLDivElement>(null);
	const scrollHandler = (e: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = e.currentTarget.scrollTop;
    console.log("scrollTop", scrollTop); // 滚动的距离
    window.history.replaceState({}, '', `top=${scrollTop}`)
  };

  useLayoutEffect(() => {
    if (!container.current) return;
    const top = window.location.search.split("=")[1];
    container.current.scrollTop = Number(top) || 0;
  })

	return (
		<div
			ref={container}
			style={{ height: "500px", overflow: "auto" }}
			onScroll={scrollHandler}
		>
			{Array.from({ length: 500 }).map((_, index) => {
				return <div key={index}>{index}</div>;
			})}
		</div>
	);

	// 使用 useEffect 实现动画效果
	// useEffect(() => {
	// 	const app1 = document.getElementById("app1") as HTMLDivElement;
	// 	app1.style.transition = "opacity 3s";
	// 	app1.style.opacity = "1";
	// }, []);

	// // 使用 useLayoutEffect 实现动画效果
	// useLayoutEffect(() => {
	// 	const app2 = document.getElementById("app2") as HTMLDivElement;
	// 	app2.style.transition = "opacity 3s";
	// 	app2.style.opacity = "1";
	// }, []);

	// return (
	// 	<div>
	// 		<div id="app1" style={{ opacity: 0 }}>
	// 			app1
	// 		</div>
	// 		<div id="app2" style={{ opacity: 0 }}>
	// 			app2
	// 		</div>
	// 	</div>
	// );
}
