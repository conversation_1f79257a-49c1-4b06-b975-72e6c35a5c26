import { Form } from "antd";
import { forwardRef, useImperativeHandle, useRef, useState } from "react";

interface ChildRef {
	name: string;
	validate: () => string | boolean;
	reset: () => void;
}

const Child = ({ ref }: { ref: React.Ref<ChildRef> }) => {
	const [form, setForm] = useState({
		username: "",
		password: "",
		email: "",
	});

	const validate = () => {
		if (!form.username) {
			return "username is required";
		}
		if (!form.password) {
			return "password is required";
		}
		if (!form.email) {
			return "email is required";
		}
		return true;
	};

	const reset = () => {
		setForm({
			username: "",
			password: "",
			email: "",
		});
	};

	useImperativeHandle(ref, () => {
		return {
			name: "Child",
			reset,
			validate,
		};
	});

	return (
		<div>
			<Form>
				<Form.Item label="username">
					<input
						type="text"
						onChange={(e) => {
							setForm({
								...form,
								username: e.target.value,
							});
						}}
					/>
				</Form.Item>
				<Form.Item label="password">
					<input
						type="password"
						onChange={(e) => {
							setForm({
								...form,
								password: e.target.value,
							});
						}}
					/>
				</Form.Item>
				<Form.Item label="email">
					<input
						type="email"
						onChange={(e) => {
							setForm({
								...form,
								email: e.target.value,
							});
						}}
					/>
				</Form.Item>
			</Form>
		</div>
	);
};

export default function UseImperativeHandle() {
	const ref = useRef<ChildRef>(null);

	return (
		<div>
			<Child ref={ref} />
			<button onClick={() => {
        console.log(ref.current?.validate())
      }}>validate</button>
			<button onClick={ref.current?.reset}>reset</button>
		</div>
	);
}

// interface ChildProps {
// 	name: string;
// 	count: number;
// 	addCount: () => void;
// 	resetCount: () => void;
// }

// react 19
// const Child = ({ref}: {ref: React.Ref<ChildProps>}) => {
// // react 18
// // const Child = forwardRef<ChildProps>((props, ref) => {
// 	const [count, setCount] = useState(0);
// 	useImperativeHandle(ref, () => {
// 		return {
// 			name: "Child",
// 			count,
// 			addCount: () => setCount(count + 1),
// 			resetCount: () => setCount(0),
// 		};
// 	});
// 	return (
// 		<div>
// 			<div>{count}</div>
// 			<h3>child</h3>
// 		</div>
// 	);
// };

// export default function UseImperativeHandle() {
// 	const childRef = useRef<ChildProps>(null);

// 	const getChildInfo = () => {
// 		console.log(childRef.current);
// 	};
// 	return (
// 		<div>
// 			<h3>Father</h3>
// 			<hr />
// 			<Child ref={childRef} />
// 			<button onClick={getChildInfo}>get child info</button>
// 			<button onClick={() => childRef.current?.addCount()}>add count</button>
// 			<button onClick={() => childRef.current?.resetCount()}>
// 				reset count
// 			</button>
// 		</div>
// 	);
// }
