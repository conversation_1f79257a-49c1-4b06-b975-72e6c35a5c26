import { useContext } from "react"
import { ThemeContext } from "../App"

const Child = () => {
  const theme = useContext(ThemeContext)
  console.log('child theme: ', theme)
  return (
    <div>Child</div>
  )
}

export default function UseContext() {
  const theme = useContext(ThemeContext)
  console.log('parent theme: ', theme)

  return (
    <div>
      <div>use context</div>
      <Child />
    </div>
  )
}
