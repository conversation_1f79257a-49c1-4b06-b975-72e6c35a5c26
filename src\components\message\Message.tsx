import './Message.css'
import ReactDom from 'react-dom/client';

interface Message {
  container: HTMLDivElement
  root: ReactDom.Root
}

const Message = () => {
  return <div className="message">Message</div>
}


const queue: Message[] = []

window.onShow = () => {
  const messageContainer = document.createElement('div')
  messageContainer.className = 'message'
  messageContainer.style.top = `${queue.length * 80}px`
  document.body.appendChild(messageContainer)
  const root = ReactDom.createRoot(messageContainer)
  root.render(<Message />)

  queue.push({
    container: messageContainer,
    root
  })

  setTimeout(() => {
    const item = queue.find(item => item.container === messageContainer)
    if (item) {
      item.root.unmount()
      document.body.removeChild(item.container)
      queue.splice(queue.indexOf(item), 1)
    }
  }, 2000)
}

declare global {
  interface Window {
    onShow: () => void
  }
}