import { useSyncExternalStore } from "react"

export const useStorage = (key: string, initialValue: any) => {
  const res = useSyncExternalStore(subscribe, getSnapshot)

  function subscribe(callback: () => void) {
    window.addEventListener('storage', callback)
    return () => {
      window.removeEventListener('storage', callback)
    }
  }

  function updateStorage(value: any) {
    localStorage.setItem(key, JSON.stringify(value))
    window.dispatchEvent(new StorageEvent('storage'))
  }

  function getSnapshot() {
    return localStorage.getItem(key) ? JSON.parse(localStorage.getItem(key)!) : initialValue
  }

  return [res, updateStorage]
}