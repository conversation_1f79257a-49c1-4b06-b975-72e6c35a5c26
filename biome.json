{"$schema": "https://biomejs.dev/schemas/1.9.4/schema.json", "vcs": {"enabled": false, "clientKind": "git", "useIgnoreFile": false}, "files": {"ignoreUnknown": false, "ignore": []}, "formatter": {"enabled": true, "formatWithErrors": false, "ignore": [], "attributePosition": "auto", "indentStyle": "tab", "indentWidth": 2, "lineWidth": 80, "lineEnding": "lf"}, "organizeImports": {"enabled": true}, "linter": {"enabled": true, "rules": {"recommended": true, "style": {"noNonNullAssertion": "off"}, "a11y": {"useKeyWithClickEvents": "off", "useButtonType": "off"}, "suspicious": {"noExplicitAny": "off", "noArrayIndexKey": "off"}, "correctness": {"useExhaustiveDependencies": "off"}}}, "javascript": {"formatter": {"quoteStyle": "double"}}}