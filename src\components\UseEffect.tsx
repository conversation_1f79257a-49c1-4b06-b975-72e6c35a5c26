import { Input } from "antd";
import { useEffect, useState } from "react";

interface UserData {
	name: string;
	email: string;
	username: string;
	phone: string;
	website: string;
}

export default function UseEffect() {
	const [userData, setUserData] = useState<UserData | null>(null);
	const [userId, setUserId] = useState<string>("1");
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		setLoading(true);
		fetch(`https://jsonplaceholder.typicode.com/users/${userId}`)
			.then((res) => res.json())
			.then((data) => {
				setUserData(data);
				setLoading(false);
			});
	}, [userId]);

	return (
		<div>
			<Input
				value={userId}
				onChange={(e) => setUserId(e.target.value)}
				type="text"
			/>
			<div>
				{loading ? (
					<p>'Loading...'</p>
				) : (
					<div>
						<p>{userData?.name}</p>
						<p>{userData?.email}</p>
						<p>{userData?.username}</p>
						<p>{userData?.phone}</p>
						<p>{userData?.website}</p>
					</div>
				)}
			</div>
		</div>
	);
}
