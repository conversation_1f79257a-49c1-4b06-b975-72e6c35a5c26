import { Input } from "antd";
import { memo, useState } from "react";

interface User {
  name: string;
  age: number;
  phone: string;
}

const UserCard = memo((props: {user: User}) => {
  console.log('render child component')
  const { user } = props;
  const styles = {
    backgroundColor: 'lightblue',
    padding: '20px',
    borderRadius: '5px',
    boxShadow: '0 0 10px rgba(0, 0, 0, 0.2)',
  }

  return (
    <div style={styles}>
      <p>{user.name}</p>
      <p>{user.age}</p>
      <p>{user.phone}</p>
    </div>
  )
})

export default function UseMemo() {
  const [input, setInput] = useState('')
  const [user, setUser] = useState<User>({
    name: '<PERSON>',
    age: 30,
    phone: '1234567890',
  })
  return (
    <div>
      <button onClick={() => setUser({
        name: '<PERSON><PERSON>',
        age: 20,
        phone: '2313',
      })}>change user</button>
      <Input value={input} onChange={(e) => setInput(e.target.value)} />
      <UserCard user={user} />
    </div>
  )
}
