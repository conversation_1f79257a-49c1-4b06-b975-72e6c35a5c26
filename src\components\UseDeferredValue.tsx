import { Input, List } from "antd";
import { useDeferredValue, useState } from "react";
import mockjs from "mockjs";

interface ListItem {
	id: number;
	name: number;
	address: string;
}

export default function UseDeferredValue() {
	const [val, setVal] = useState("");
	const [list] = useState<ListItem[]>(() => {
		return mockjs.mock({
			"list|1000": [
				{
					"id|+1": 1,
					name: "@natural",
					address: "@county(true)",
				},
			],
		}).list;
	});

	const deferredValue = useDeferredValue(val);
	const isPending = val !== deferredValue;

	const queryItem = () => {
		return list.filter((item) => item.name.toString().includes(deferredValue));
	};

	return (
		<div>
			<Input value={val} onChange={(e) => setVal(e.target.value)} />
			<List
				style={{ opacity: isPending ? 0.5 : 1, transition: "opacity 0.5s" }}
				bordered
				dataSource={queryItem()}
				renderItem={(item) => (
					<List.Item key={item.id}>
						<List.Item.Meta title={item.name} description={item.address} />
					</List.Item>
				)}
			/>
		</div>
	);
}
